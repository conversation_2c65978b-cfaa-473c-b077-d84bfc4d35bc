from django.db import models
from Authentication.models import *

class LikeProfile(models.Model):
    user = models.ForeignKey(User,on_delete=models.CASCADE)
    liked_profile = models.ForeignKey(UserProfile,models.CASCADE)
    is_accepted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now=True)

class DislikeProfile(models.Model):
    user = models.ForeignKey(User,on_delete=models.CASCADE)
    disliked_profile = models.ForeignKey(UserProfile,models.CASCADE)
    created_at = models.DateTimeField(auto_now=True)

class ChatMessage(models.Model):
    from_user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='chat_from_user')
    to_user = models.ForeignKey(User,on_delete=models.CASCADE,related_name='chat_to_user')
    is_read = models.BooleanField(default=False)
    read_time = models.CharField(max_length=250,default='')
    type = models.CharField(max_length=250)
    file = models.FileField(upload_to='chat_files/',blank=True,null=True)
    message_id = models.CharField(max_length=250,default='')
    messages = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

