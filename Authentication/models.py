from django.db import models

class User(models.Model):
    email = models.Email<PERSON>ield(unique=True)
    password = models.CharField(max_length=255)
    one_signal_id = models.CharField(max_length=255,default='')
    created_at = models.DateTimeField(auto_now_add=True)
    otp = models.CharField(max_length=10,default='')
    is_delete = models.BooleanField(default=False)

    def __str__(self):
        return self.email
    
class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    profile_picture = models.FileField(upload_to='profile_pictures/', null=True, blank=True)
    contact_number = models.CharField(max_length=255)
    full_name = models.CharField(max_length=255)
    dob = models.CharField(max_length=255)
    gender = models.Char<PERSON>ield(max_length=255)
    prefered_lease_period = models.JSONField(default=list)
    prefered_gender = models.Char<PERSON><PERSON>(default='3') # 1: Male, 2: Female, 3: Both
    prefered_locations = models.J<PERSON><PERSON><PERSON>(default=list)
    prefered_smoking = models.CharField(default='4') # 1: Social, 2: Social Smoker, 3: Ocassionaly , 4: Non-smoker
    cleaniness = models.CharField(default='1') # 1: Very Tidey, 2: Messy, 3: Average
    is_having_pet = models.BooleanField(default=False)
    personality_type_description = models.JSONField(default=list)
    class_standing = models.CharField(default='1') # 1: Freshman, 2: Sophomore, 3: Junior , 4: Senior
    habits_lifestyle = models.JSONField(default=list)
    living_style = models.JSONField(default=list)
    interests_hobbies = models.JSONField(default=list)
    about = models.TextField(null=True, blank=True,default='')
    is_verified = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.user.email
    
class UserProfilePictures(models.Model):
    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    picture = models.FileField(upload_to='profile_pictures/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.user_profile.user.email
    
class HabitsLifestyle(models.Model):
    name = models.CharField(max_length=255)
    icon = models.FileField(upload_to='habits_lifestyle_icons/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name
    
class LivingStyle(models.Model):
    name = models.CharField(max_length=255)
    icon = models.FileField(upload_to='living_style_icons/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name
    
class InterestsHobbies(models.Model):
    name = models.CharField(max_length=255)
    icon = models.FileField(upload_to='interests_hobbies_icons/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name
    
class ReportProfile(models.Model):
    user = models.ForeignKey(User,on_delete=models.CASCADE)
    profile = models.ForeignKey(UserProfile,on_delete=models.CASCADE)
    reason = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

class BlockProfile(models.Model):
    user = models.ForeignKey(User,on_delete=models.CASCADE)
    blocked_profile = models.ForeignKey(UserProfile,on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    